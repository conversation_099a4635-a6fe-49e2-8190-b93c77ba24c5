import { useEffect } from 'react';
import { useLocation } from 'wouter';

// Google Analytics 配置
const GA_MEASUREMENT_ID = 'G-RCM4MX2590';

// 类型定义
interface GAEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  custom_parameter?: string;
  [key: string]: any;
}

interface GAPageViewParams {
  page_title?: string;
  page_location?: string;
  page_path?: string;
  content_group1?: string; // 可用于语言分组
  content_group2?: string; // 可用于页面类型分组
}

/**
 * 检查 gtag 是否可用
 */
function isGtagAvailable(): boolean {
  return typeof window !== 'undefined' && typeof window.gtag === 'function';
}

/**
 * 发送页面浏览事件
 */
export function trackPageView(params?: GAPageViewParams): void {
  if (!isGtagAvailable()) {
    console.warn('Google Analytics (gtag) 不可用');
    return;
  }

  const defaultParams: GAPageViewParams = {
    page_title: document.title,
    page_location: window.location.href,
    page_path: window.location.pathname + window.location.search,
  };

  const finalParams = { ...defaultParams, ...params };

  window.gtag('config', GA_MEASUREMENT_ID, finalParams);

  // 开发环境日志
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('📊 GA Page View:', finalParams);
  }
}

/**
 * 发送自定义事件
 */
export function trackEvent(eventName: string, params?: GAEventParams): void {
  if (!isGtagAvailable()) {
    console.warn('Google Analytics (gtag) 不可用');
    return;
  }

  const finalParams = {
    send_to: GA_MEASUREMENT_ID,
    ...params,
  };

  window.gtag('event', eventName, finalParams);

  // 开发环境日志
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log(`📊 GA Event: ${eventName}`, finalParams);
  }
}

/**
 * 跟踪打字测试相关事件
 */
export const trackTypingTestEvents = {
  // 测试开始
  testStart: (language: string, difficulty: string) => {
    trackEvent('typing_test_start', {
      event_category: 'typing_test',
      event_label: `${language}_${difficulty}`,
      custom_parameter: 'test_started',
    });
  },

  // 测试完成
  testComplete: (wpm: number, accuracy: number, language: string, difficulty: string) => {
    trackEvent('typing_test_complete', {
      event_category: 'typing_test',
      event_label: `${language}_${difficulty}`,
      value: Math.round(wpm),
      custom_parameter: `wpm_${wpm}_accuracy_${accuracy}`,
    });
  },

  // 语言切换
  languageChange: (fromLang: string, toLang: string) => {
    trackEvent('language_change', {
      event_category: 'user_interaction',
      event_label: `${fromLang}_to_${toLang}`,
      custom_parameter: 'language_switched',
    });
  },

  // 难度切换
  difficultyChange: (fromDiff: string, toDiff: string) => {
    trackEvent('difficulty_change', {
      event_category: 'user_interaction',
      event_label: `${fromDiff}_to_${toDiff}`,
      custom_parameter: 'difficulty_switched',
    });
  },

  // 测试重启
  testRestart: () => {
    trackEvent('typing_test_restart', {
      event_category: 'user_interaction',
      event_label: 'restart_button',
      custom_parameter: 'test_restarted',
    });
  },

  // 结果分享
  resultShare: (platform: string) => {
    trackEvent('result_share', {
      event_category: 'social',
      event_label: platform,
      custom_parameter: 'result_shared',
    });
  },
};

/**
 * 跟踪导航事件
 */
export const trackNavigationEvents = {
  // 页面访问
  pageVisit: (pageName: string, language: string) => {
    trackPageView({
      page_title: `${pageName} - TypingTest`,
      content_group1: language,
      content_group2: pageName,
    });
  },

  // 外部链接点击
  externalLinkClick: (url: string) => {
    trackEvent('click', {
      event_category: 'outbound',
      event_label: url,
      custom_parameter: 'external_link_clicked',
    });
  },
};

/**
 * React Hook: 自动跟踪路由变化
 */
export function useAnalytics(pageName?: string, language?: string): void {
  const [location] = useLocation();

  useEffect(() => {
    // 延迟执行以确保 gtag 已加载
    const timer = setTimeout(() => {
      if (pageName && language) {
        trackNavigationEvents.pageVisit(pageName, language);
      } else {
        // 默认页面浏览跟踪
        trackPageView({
          page_path: location,
        });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [location, pageName, language]);
}

/**
 * React Hook: 跟踪用户交互
 */
export function useTypingTestAnalytics() {
  return {
    trackTestStart: trackTypingTestEvents.testStart,
    trackTestComplete: trackTypingTestEvents.testComplete,
    trackLanguageChange: trackTypingTestEvents.languageChange,
    trackDifficultyChange: trackTypingTestEvents.difficultyChange,
    trackTestRestart: trackTypingTestEvents.testRestart,
    trackResultShare: trackTypingTestEvents.resultShare,
  };
}

/**
 * 初始化 Google Analytics
 * 在应用启动时调用
 */
export function initializeAnalytics(): void {
  // 检查 gtag 是否可用
  if (!isGtagAvailable()) {
    console.warn('Google Analytics 未正确加载');
    return;
  }

  // 设置用户属性
  window.gtag('config', GA_MEASUREMENT_ID, {
    // 自定义维度
    'custom_map': {
      'dimension1': 'language',
      'dimension2': 'difficulty',
      'dimension3': 'device_type',
    },
  });

  // 开发环境信息
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('🚀 Google Analytics 已初始化');
    console.log('📊 测量 ID:', GA_MEASUREMENT_ID);
  }
}

// 导出默认配置
export const GA_CONFIG = {
  MEASUREMENT_ID: GA_MEASUREMENT_ID,
  isDebugMode: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
};
