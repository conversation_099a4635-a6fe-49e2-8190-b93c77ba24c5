import { useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { getTranslation } from "@/lib/i18n";
import { MobileInputHandler } from "@/components/mobile-input-handler";
import { useDeviceInfo } from "@/hooks/use-mobile";
import type { TypingTestState } from "@/hooks/use-typing-test";

interface TypingTestProps {
  language: string;
  difficulty: string;
  testState: TypingTestState;
  isTestActive: boolean;
  onKeyPress: (key: string) => void;
  onCompositionInput: (text: string) => void;
  progress: number;
}

export function TypingTest({
  language,
  difficulty,
  testState,
  isTestActive,
  onKeyPress,
  onCompositionInput,
  progress
}: TypingTestProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const { isMobileTouch } = useDeviceInfo();

  const { data: textSample, isLoading, error } = useQuery({
    queryKey: [`/api/text-samples/${language}/${difficulty}`],
    enabled: !!language && !!difficulty,
  });

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isTestActive) return;

      // Prevent default for most keys except special ones
      if (e.key.length === 1 || e.key === 'Backspace') {
        e.preventDefault();
        onKeyPress(e.key);
      }
    };

    const handleFocus = () => {
      if (isTestActive && inputRef.current) {
        inputRef.current.focus();
      }
    };

    // Auto-focus on mobile devices when test becomes active
    if (isTestActive && inputRef.current) {
      // Small delay to ensure the input is ready
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleFocus);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleFocus);
    };
  }, [isTestActive, onKeyPress]);

  const renderText = () => {
    if (!testState.testText) return null;

    return (
      <div
        className="typing-area text-slate-800 select-none break-words whitespace-pre-wrap"
        style={{
          fontFamily: language === 'zh' ? '"Noto Sans SC", "Microsoft YaHei", sans-serif' : '"Inter", system-ui, sans-serif',
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
      >
        {testState.testText.split('').map((char, index) => {
          let className = 'char-upcoming';
          
          if (index < testState.currentPosition) {
            className = testState.typedCharacters[index] === char ? 'char-correct' : 'char-incorrect';
          } else if (index === testState.currentPosition) {
            className = 'char-current';
          }

          return (
            <span key={index} className={className}>
              {char === ' ' ? '\u00A0' : char}
            </span>
          );
        })}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-8 mb-8">
        <div className="space-y-4">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-8 mb-8">
        <div className="text-center text-red-600">
          Failed to load text sample. Please try again.
        </div>
      </div>
    );
  }

  return (
    <MobileInputHandler
      isActive={isTestActive}
      onKeyPress={onKeyPress}
      onCompositionInput={onCompositionInput}
    >
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2 sm:gap-0">
            <h2 className="text-base sm:text-lg font-semibold text-slate-800 truncate">
              {(textSample && typeof textSample === 'object' && 'title' in textSample) ? (textSample.title as string) : getTranslation(language, 'appName')}
            </h2>
            <div className="flex items-center space-x-2">
              <Progress value={progress} className="w-24 sm:w-32" />
              <span className="text-xs sm:text-sm text-slate-500 whitespace-nowrap">
                {Math.round(progress)}%
              </span>
            </div>
          </div>

          <div
            className="bg-slate-50 rounded-lg p-4 sm:p-6 border-2 border-slate-200 focus-within:border-blue-400 transition-colors duration-200 cursor-text min-h-[120px] sm:min-h-[140px] no-zoom"
            onClick={() => !isMobileTouch && inputRef.current?.focus()}
          >
            {renderText()}
          </div>

          {/* Hidden input for desktop typing - only used on non-touch devices */}
          {!isMobileTouch && (
            <input
              ref={inputRef}
              type="text"
              className="absolute -left-9999px opacity-0 pointer-events-none"
              autoComplete="off"
              autoCorrect="off"
              autoCapitalize="off"
              spellCheck="false"
              onChange={() => {}} // Controlled by keydown events
              onCompositionEnd={(e) => {
                if (isTestActive && e.data) {
                  onCompositionInput(e.data);
                }
              }}
            />
          )}

          <div className="mt-4 text-xs sm:text-sm text-slate-500 text-center">
            <p className="mb-2">
              {isTestActive
                ? getTranslation(language, 'typeTextAbove')
                : isMobileTouch
                  ? getTranslation(language, 'clickToStart')
                  : getTranslation(language, 'clickToStart')
              }
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800">
                <span className="w-2 h-2 bg-green-600 rounded-full mr-1"></span>
                {getTranslation(language, 'correct')}
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-red-100 text-red-800">
                <span className="w-2 h-2 bg-red-600 rounded-full mr-1"></span>
                {getTranslation(language, 'incorrect')}
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-amber-100 text-amber-800">
                <span className="w-2 h-2 bg-amber-600 rounded-full mr-1"></span>
                {getTranslation(language, 'current')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </MobileInputHandler>
  );
}
