import { useEffect } from 'react';
import { useDeviceInfo } from '@/hooks/use-mobile';

interface MobileOptimizationsProps {
  children: React.ReactNode;
}

export function MobileOptimizations({ children }: MobileOptimizationsProps) {
  const { isMobile, isTouchDevice } = useDeviceInfo();

  useEffect(() => {
    // Prevent zoom on double tap for iOS
    if (isMobile) {
      let lastTouchEnd = 0;
      const preventZoom = (e: TouchEvent) => {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
          e.preventDefault();
        }
        lastTouchEnd = now;
      };

      document.addEventListener('touchend', preventZoom, { passive: false });
      
      return () => {
        document.removeEventListener('touchend', preventZoom);
      };
    }
  }, [isMobile]);

  useEffect(() => {
    // Add mobile-specific classes to body
    if (isMobile) {
      document.body.classList.add('mobile-device');
    }
    if (isTouchDevice) {
      document.body.classList.add('touch-device');
    }

    return () => {
      document.body.classList.remove('mobile-device', 'touch-device');
    };
  }, [isMobile, isTouchDevice]);

  useEffect(() => {
    // Handle viewport height changes on mobile (keyboard appearance)
    if (isMobile) {
      const setVH = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      };

      setVH();
      window.addEventListener('resize', setVH);
      window.addEventListener('orientationchange', setVH);

      return () => {
        window.removeEventListener('resize', setVH);
        window.removeEventListener('orientationchange', setVH);
      };
    }
  }, [isMobile]);

  return <>{children}</>;
}

// CSS to be added to index.css
export const mobileOptimizationStyles = `
  .mobile-device {
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Use dynamic viewport height */
    min-height: calc(var(--vh, 1vh) * 100);
  }

  .touch-device {
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
  }

  /* Improve button touch targets on mobile */
  @media (hover: none) and (pointer: coarse) {
    button, [role="button"], input[type="button"], input[type="submit"] {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Prevent zoom on input focus for iOS */
  @media screen and (max-width: 767px) {
    input, select, textarea {
      font-size: 16px !important;
      transform: translateZ(0);
    }
  }

  /* Improve focus states for keyboard navigation */
  @media (hover: hover) and (pointer: fine) {
    button:focus-visible,
    input:focus-visible,
    select:focus-visible,
    textarea:focus-visible {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }
  }

  /* Touch-specific styles */
  @media (hover: none) and (pointer: coarse) {
    /* Larger tap targets */
    .virtual-key {
      min-height: 44px;
      min-width: 44px;
      padding: 8px;
    }

    /* Better visual feedback for touches */
    button:active,
    .virtual-key:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }

    /* Remove hover effects on touch devices */
    button:hover,
    .virtual-key:hover {
      background-color: initial;
    }
  }
`;
