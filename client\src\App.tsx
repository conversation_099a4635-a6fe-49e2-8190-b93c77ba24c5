import { Switch, Route } from "wouter";
import { useEffect } from "react";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { MobileOptimizations } from "@/components/mobile-optimizations";
import { ErrorBoundary } from "@/components/error-boundary";
import { initializeAnalytics } from "@/hooks/use-analytics";
import Home from "@/pages/home";
import NotFound from "@/pages/not-found";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";

function Router() {
  return (
    <Switch>
      {/* Language-prefixed routes */}
      <Route path="/:lang" component={Home} />
      <Route path="/:lang/privacy-policy" component={PrivacyPolicy} />
      <Route path="/:lang/terms-of-service" component={TermsOfService} />

      {/* Legacy routes without language prefix - will be redirected */}
      <Route path="/" component={Home} />
      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route path="/terms-of-service" component={TermsOfService} />

      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  // 初始化 Google Analytics
  useEffect(() => {
    // 延迟初始化以确保 gtag 脚本已加载
    const timer = setTimeout(() => {
      initializeAnalytics();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <LanguageProvider>
            <MobileOptimizations>
              <Toaster />
              <Router />
            </MobileOptimizations>
          </LanguageProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
