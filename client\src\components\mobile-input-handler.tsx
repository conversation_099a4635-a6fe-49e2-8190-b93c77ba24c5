import { useEffect, useRef, useState } from 'react';
import { useDeviceInfo } from '@/hooks/use-mobile';

interface MobileInputHandlerProps {
  isActive: boolean;
  onKeyPress: (key: string) => void;
  onCompositionInput: (text: string) => void;
  children: React.ReactNode;
}

export function MobileInputHandler({
  isActive,
  onKeyPress,
  onCompositionInput,
  children
}: MobileInputHandlerProps) {
  const { isMobileTouch } = useDeviceInfo();
  const inputRef = useRef<HTMLInputElement>(null);
  const [isComposing, setIsComposing] = useState(false);

  useEffect(() => {
    if (isActive && isMobileTouch && inputRef.current) {
      // Focus the input when test becomes active on mobile
      const focusInput = () => {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      };

      focusInput();

      // Re-focus if the input loses focus during the test
      const handleBlur = () => {
        if (isActive) {
          setTimeout(() => {
            inputRef.current?.focus();
          }, 50);
        }
      };

      inputRef.current.addEventListener('blur', handleBlur);

      return () => {
        inputRef.current?.removeEventListener('blur', handleBlur);
      };
    }
  }, [isActive, isMobileTouch]);

  const handleInput = (e: React.FormEvent<HTMLInputElement>) => {
    if (!isActive || isComposing) return;

    const target = e.target as HTMLInputElement;
    const value = target.value;

    if (value) {
      // Process each character
      for (const char of value) {
        onKeyPress(char);
      }
      // Clear the input
      target.value = '';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isActive) return;

    if (e.key === 'Backspace') {
      e.preventDefault();
      onKeyPress('Backspace');
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    setIsComposing(false);
    if (isActive && e.data) {
      onCompositionInput(e.data);
      // Clear the input
      (e.target as HTMLInputElement).value = '';
    }
  };

  if (!isMobileTouch) {
    return <>{children}</>;
  }

  return (
    <div className="relative">
      {children}
      
      {/* Mobile input overlay */}
      {isActive && (
        <input
          ref={inputRef}
          type="text"
          className="fixed top-0 left-0 w-full h-full opacity-0 pointer-events-auto z-50"
          style={{
            fontSize: '16px', // Prevent zoom on iOS
            background: 'transparent',
            border: 'none',
            outline: 'none',
            caretColor: 'transparent'
          }}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
          inputMode="text"
          enterKeyHint="next"
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onCompositionStart={handleCompositionStart}
          onCompositionEnd={handleCompositionEnd}
          placeholder=""
        />
      )}
    </div>
  );
}
