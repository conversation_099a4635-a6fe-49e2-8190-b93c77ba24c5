import { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { calculateWPM, calculateAccuracy } from "@/lib/typing-utils";

export interface TypingTestState {
  testText: string;
  currentPosition: number;
  typedCharacters: string[];
  correctCharacters: number;
  errors: number;
  startTime: number | null;
  endTime: number | null;
  timeRemaining: number;
  wpm: number;
  accuracy: number;
}

export function useTypingTest(language: string, difficulty: string, duration: number) {
  const [testState, setTestState] = useState<TypingTestState>({
    testText: "",
    currentPosition: 0,
    typedCharacters: [],
    correctCharacters: 0,
    errors: 0,
    startTime: null,
    endTime: null,
    timeRemaining: duration,
    wpm: 0,
    accuracy: 100,
  });

  const [isTestActive, setIsTestActive] = useState(false);
  const [isTestCompleted, setIsTestCompleted] = useState(false);

  const queryClient = useQueryClient();

  // Fetch text sample
  const { data: textSample } = useQuery({
    queryKey: [`/api/text-samples/${language}/${difficulty}`],
    enabled: !!language && !!difficulty,
  });

  // Update test text when sample changes
  useEffect(() => {
    if (textSample && typeof textSample === 'object' && 'content' in textSample) {
      setTestState(prev => ({
        ...prev,
        testText: textSample.content as string,
        currentPosition: 0,
        typedCharacters: [],
        correctCharacters: 0,
        errors: 0,
      }));
    }
  }, [textSample]);

  // Timer effect
  useEffect(() => {
    if (!isTestActive || testState.timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTestState(prev => {
        const newTimeRemaining = prev.timeRemaining - 1;
        if (newTimeRemaining <= 0) {
          setIsTestActive(false);
          setIsTestCompleted(true);
          return { ...prev, timeRemaining: 0, endTime: Date.now() };
        }
        return { ...prev, timeRemaining: newTimeRemaining };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isTestActive, testState.timeRemaining]);

  // Calculate real-time statistics
  useEffect(() => {
    if (testState.startTime) {
      const timeElapsed = isTestActive 
        ? (Date.now() - testState.startTime) / 1000 / 60 
        : (testState.endTime! - testState.startTime) / 1000 / 60;
      
      const wpm = calculateWPM(testState.correctCharacters, timeElapsed);
      const accuracy = calculateAccuracy(testState.correctCharacters, testState.errors);
      
      setTestState(prev => ({ ...prev, wpm, accuracy }));
    }
  }, [testState.correctCharacters, testState.errors, testState.startTime, testState.endTime, isTestActive]);

  const startTest = useCallback(() => {
    if (!testState.testText) return;
    
    setTestState(prev => ({
      ...prev,
      currentPosition: 0,
      typedCharacters: [],
      correctCharacters: 0,
      errors: 0,
      startTime: Date.now(),
      endTime: null,
      timeRemaining: duration,
      wpm: 0,
      accuracy: 100,
    }));
    setIsTestActive(true);
    setIsTestCompleted(false);
  }, [testState.testText, duration]);

  const resetTest = useCallback(() => {
    setTestState(prev => ({
      ...prev,
      currentPosition: 0,
      typedCharacters: [],
      correctCharacters: 0,
      errors: 0,
      startTime: null,
      endTime: null,
      timeRemaining: duration,
      wpm: 0,
      accuracy: 100,
    }));
    setIsTestActive(false);
    setIsTestCompleted(false);
  }, [duration]);

  const handleKeyPress = useCallback((key: string) => {
    if (!isTestActive || !testState.testText) return;

    if (key === 'Backspace') {
      // Handle backspace
      if (testState.currentPosition > 0) {
        setTestState(prev => {
          // Recalculate correctCharacters and errors when backspacing
          const newPosition = prev.currentPosition - 1;
          const newTypedCharacters = prev.typedCharacters.slice(0, -1);

          let newCorrectCharacters = 0;
          let newErrors = 0;

          for (let i = 0; i < newTypedCharacters.length; i++) {
            if (newTypedCharacters[i] === prev.testText[i]) {
              newCorrectCharacters++;
            } else {
              newErrors++;
            }
          }

          return {
            ...prev,
            currentPosition: newPosition,
            typedCharacters: newTypedCharacters,
            correctCharacters: newCorrectCharacters,
            errors: newErrors,
          };
        });
      }
      return;
    }

    // Handle single character input (including Chinese characters)
    if (key.length !== 1) return;

    const currentChar = testState.testText[testState.currentPosition];
    const isCorrect = key === currentChar;

    setTestState(prev => {
      const newPosition = prev.currentPosition + 1;
      const newTypedCharacters = [...prev.typedCharacters, key];

      const newState = {
        ...prev,
        currentPosition: newPosition,
        typedCharacters: newTypedCharacters,
        correctCharacters: isCorrect ? prev.correctCharacters + 1 : prev.correctCharacters,
        errors: isCorrect ? prev.errors : prev.errors + 1,
      };

      // Check if test is complete
      if (newPosition >= prev.testText.length) {
        setIsTestActive(false);
        setIsTestCompleted(true);
        newState.endTime = Date.now();
      }

      return newState;
    });
  }, [isTestActive, testState.testText, testState.currentPosition]);

  const handleCompositionInput = useCallback((text: string) => {
    if (!isTestActive || !testState.testText || !text) return;

    // Handle composition input (for IME) - process the entire text at once
    setTestState(prev => {
      const chars = Array.from(text);
      let newPosition = prev.currentPosition;
      let newTypedCharacters = [...prev.typedCharacters];
      let newCorrectCharacters = prev.correctCharacters;
      let newErrors = prev.errors;

      for (const char of chars) {
        if (newPosition >= prev.testText.length) break;

        const currentChar = prev.testText[newPosition];
        const isCorrect = char === currentChar;

        newTypedCharacters.push(char);
        if (isCorrect) {
          newCorrectCharacters++;
        } else {
          newErrors++;
        }
        newPosition++;
      }

      const newState = {
        ...prev,
        currentPosition: newPosition,
        typedCharacters: newTypedCharacters,
        correctCharacters: newCorrectCharacters,
        errors: newErrors,
      };

      // Check if test is complete
      if (newPosition >= prev.testText.length) {
        setIsTestActive(false);
        setIsTestCompleted(true);
        newState.endTime = Date.now();
      }

      return newState;
    });
  }, [isTestActive, testState.testText]);

  const progress = testState.testText 
    ? (testState.currentPosition / testState.testText.length) * 100 
    : 0;

  return {
    testState,
    isTestActive,
    isTestCompleted,
    startTest,
    resetTest,
    handleKeyPress,
    handleCompositionInput,
    progress,
  };
}
