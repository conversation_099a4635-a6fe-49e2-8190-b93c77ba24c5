// Google Analytics 检查脚本
// 在浏览器控制台中运行此脚本来检查 Google Analytics 的加载状态

function checkGoogleAnalytics() {
  console.log('🔍 Google Analytics 检查开始...\n');
  
  const results = {
    scriptLoaded: false,
    gtagFunction: false,
    dataLayer: false,
    networkRequests: false,
    configCalled: false,
    errors: []
  };

  // 1. 检查 gtag 脚本是否已加载
  console.log('1. 检查 gtag 脚本标签...');
  const gtagScript = document.querySelector('script[src*="googletagmanager.com/gtag/js"]');
  if (gtagScript) {
    results.scriptLoaded = true;
    console.log('✅ 找到 gtag 脚本标签:', gtagScript.src);
    console.log('   - async:', gtagScript.async);
    console.log('   - 加载状态:', gtagScript.readyState || '未知');
  } else {
    results.errors.push('❌ 未找到 gtag 脚本标签');
    console.log('❌ 未找到 gtag 脚本标签');
  }

  // 2. 检查 gtag 函数是否可用
  console.log('\n2. 检查 gtag 函数...');
  if (typeof window.gtag === 'function') {
    results.gtagFunction = true;
    console.log('✅ gtag 函数已定义');
  } else {
    results.errors.push('❌ gtag 函数未定义');
    console.log('❌ gtag 函数未定义');
  }

  // 3. 检查 dataLayer 是否存在
  console.log('\n3. 检查 dataLayer...');
  if (window.dataLayer && Array.isArray(window.dataLayer)) {
    results.dataLayer = true;
    console.log('✅ dataLayer 已定义，长度:', window.dataLayer.length);
    console.log('   dataLayer 内容:', window.dataLayer);
  } else {
    results.errors.push('❌ dataLayer 未定义或不是数组');
    console.log('❌ dataLayer 未定义或不是数组');
  }

  // 4. 检查网络请求
  console.log('\n4. 检查网络请求...');
  console.log('请在 Network 标签中查找以下请求:');
  console.log('   - https://www.googletagmanager.com/gtag/js?id=G-RCM4MX2590');
  console.log('   - https://www.google-analytics.com/g/collect (数据发送请求)');
  
  // 5. 检查配置是否被调用
  console.log('\n5. 检查 GA 配置...');
  if (window.dataLayer && window.dataLayer.some(item => 
    Array.isArray(item) && item[0] === 'config' && item[1] === 'G-RCM4MX2590'
  )) {
    results.configCalled = true;
    console.log('✅ GA 配置已调用');
  } else {
    results.errors.push('❌ GA 配置未调用');
    console.log('❌ GA 配置未调用');
  }

  // 6. 检查可能的阻止因素
  console.log('\n6. 检查可能的阻止因素...');
  
  // 检查广告拦截器
  const testDiv = document.createElement('div');
  testDiv.innerHTML = '&nbsp;';
  testDiv.className = 'adsbox';
  document.body.appendChild(testDiv);
  
  setTimeout(() => {
    if (testDiv.offsetHeight === 0) {
      console.log('⚠️  可能存在广告拦截器');
      results.errors.push('可能存在广告拦截器');
    } else {
      console.log('✅ 未检测到广告拦截器');
    }
    document.body.removeChild(testDiv);
  }, 100);

  // 检查 CSP (Content Security Policy)
  const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  if (metaCSP) {
    console.log('⚠️  发现 CSP 策略:', metaCSP.content);
    if (!metaCSP.content.includes('googletagmanager.com')) {
      results.errors.push('CSP 策略可能阻止了 Google Analytics');
    }
  }

  // 7. 提供调试建议
  console.log('\n7. 调试建议:');
  console.log('   a) 打开 Network 标签，刷新页面，查找 googletagmanager.com 请求');
  console.log('   b) 在 Console 中运行: window.gtag("event", "test_event")');
  console.log('   c) 检查是否有浏览器扩展阻止了脚本');
  console.log('   d) 尝试在隐身模式下访问页面');

  // 8. 总结
  console.log('\n📊 检查结果总结:');
  console.log('脚本标签加载:', results.scriptLoaded ? '✅' : '❌');
  console.log('gtag 函数可用:', results.gtagFunction ? '✅' : '❌');
  console.log('dataLayer 存在:', results.dataLayer ? '✅' : '❌');
  console.log('配置已调用:', results.configCalled ? '✅' : '❌');
  
  if (results.errors.length > 0) {
    console.log('\n❌ 发现的问题:');
    results.errors.forEach(error => console.log('   -', error));
  } else {
    console.log('\n🎉 Google Analytics 配置看起来正常！');
  }

  return results;
}

// 手动测试 gtag 函数
function testGtag() {
  console.log('\n🧪 测试 gtag 函数...');
  
  if (typeof window.gtag === 'function') {
    try {
      window.gtag('event', 'test_event', {
        'custom_parameter': 'test_value',
        'debug_mode': true
      });
      console.log('✅ gtag 测试事件已发送');
      console.log('   检查 Network 标签中是否有新的 collect 请求');
    } catch (error) {
      console.log('❌ gtag 函数调用失败:', error);
    }
  } else {
    console.log('❌ gtag 函数不可用');
  }
}

// 检查页面源代码
function checkPageSource() {
  console.log('\n📄 检查页面源代码...');
  
  const htmlContent = document.documentElement.outerHTML;
  
  if (htmlContent.includes('googletagmanager.com/gtag/js')) {
    console.log('✅ 页面源代码包含 gtag 脚本');
  } else {
    console.log('❌ 页面源代码不包含 gtag 脚本');
  }
  
  if (htmlContent.includes('G-RCM4MX2590')) {
    console.log('✅ 页面源代码包含 GA 测量 ID');
  } else {
    console.log('❌ 页面源代码不包含 GA 测量 ID');
  }
  
  // 显示相关的脚本标签
  const scripts = document.querySelectorAll('script');
  console.log('\n📜 页面中的所有脚本标签:');
  scripts.forEach((script, index) => {
    if (script.src) {
      console.log(`${index + 1}. ${script.src}`);
    } else if (script.innerHTML.includes('gtag') || script.innerHTML.includes('dataLayer')) {
      console.log(`${index + 1}. 内联脚本 (包含 gtag/dataLayer)`);
    }
  });
}

// 导出函数供控制台使用
window.checkGoogleAnalytics = checkGoogleAnalytics;
window.testGtag = testGtag;
window.checkPageSource = checkPageSource;

console.log('🚀 Google Analytics 检查工具已加载！');
console.log('使用方法:');
console.log('  checkGoogleAnalytics() - 完整检查');
console.log('  testGtag() - 测试 gtag 函数');
console.log('  checkPageSource() - 检查页面源代码');
