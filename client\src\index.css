@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 220 13% 97%; /* #f8fafc */
  --foreground: 215 25% 27%; /* #334155 */
  --muted: 210 11% 96%; /* #f1f5f9 */
  --muted-foreground: 215 16% 47%; /* #64748b */
  --popover: 0 0% 100%;
  --popover-foreground: 215 25% 27%;
  --card: 0 0% 100%;
  --card-foreground: 215 25% 27%;
  --border: 220 13% 91%; /* #e2e8f0 */
  --input: 220 13% 91%;
  --primary: 221 83% 53%; /* #3b82f6 */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 11% 96%;
  --secondary-foreground: 215 25% 27%;
  --accent: 210 11% 96%;
  --accent-foreground: 215 25% 27%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 215 25% 27%;
  --radius: 0.75rem;
  
  /* Custom typing test colors */
  --correct: 142 76% 36%; /* #16a34a */
  --incorrect: 0 84% 60%; /* #dc2626 */
  --current: 45 93% 47%; /* #f59e0b */
  --upcoming: 215 16% 47%; /* #64748b */
}

.dark {
  --background: 224 71% 4%;
  --foreground: 213 31% 91%;
  --muted: 223 47% 11%;
  --muted-foreground: 215 20% 65%;
  --popover: 224 71% 4%;
  --popover-foreground: 213 31% 91%;
  --card: 224 71% 4%;
  --card-foreground: 213 31% 91%;
  --border: 216 34% 17%;
  --input: 216 34% 17%;
  --primary: 210 40% 98%;
  --primary-foreground: 222 47% 11%;
  --secondary: 222 47% 11%;
  --secondary-foreground: 213 31% 91%;
  --accent: 216 34% 17%;
  --accent-foreground: 213 31% 91%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 213 31% 91%;
  --ring: 216 34% 17%;
  --radius: 0.75rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
    /* Improve text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent text size adjustment on mobile */
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Improve touch targets on mobile */
  @media (hover: none) and (pointer: coarse) {
    button, [role="button"], input, select, textarea {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Prevent zoom on input focus for iOS */
  @media screen and (max-width: 767px) {
    input, select, textarea {
      font-size: 16px !important;
    }
  }
}

@layer components {
  .char-correct {
    @apply text-green-600 bg-green-50;
    color: hsl(var(--correct));
    background-color: hsl(var(--correct) / 0.1);
  }

  .char-incorrect {
    @apply text-red-600 bg-red-50;
    color: hsl(var(--incorrect));
    background-color: hsl(var(--incorrect) / 0.1);
  }

  .char-current {
    @apply bg-amber-200 text-amber-800;
    color: hsl(var(--current));
    background-color: hsl(var(--current) / 0.3);
    animation: blink 1s infinite;
  }

  .char-upcoming {
    color: hsl(var(--upcoming));
  }

  .virtual-key {
    @apply inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md shadow-sm hover:bg-slate-50 transition-colors duration-150 min-w-[2.5rem] h-10;
    /* Improve touch targets on mobile */
    @media (hover: none) and (pointer: coarse) {
      @apply min-h-[44px] min-w-[44px] px-2 py-2 text-base;
    }
    /* Add touch feedback */
    @media (hover: none) {
      &:active {
        @apply bg-slate-200 scale-95;
        transition: all 0.1s ease;
      }
    }
  }

  .virtual-key.active {
    @apply bg-blue-100 border-blue-400 text-blue-700;
    /* Enhanced active state for mobile */
    @media (hover: none) and (pointer: coarse) {
      @apply bg-blue-200 border-blue-500 text-blue-800 shadow-md;
    }
  }

  /* Mobile-optimized typing area */
  .typing-area {
    @apply text-lg leading-relaxed;
    /* Larger text on mobile for better readability */
    @media (max-width: 767px) {
      @apply text-xl leading-loose;
      line-height: 1.8;
    }
  }

  /* Mobile-optimized statistics cards */
  .stat-card {
    @apply bg-white rounded-xl shadow-sm border border-slate-200 p-6;
    /* Adjust padding on mobile */
    @media (max-width: 767px) {
      @apply p-4;
    }
  }

  /* Responsive grid improvements */
  .stats-grid {
    @apply grid gap-4;
    /* Single column on mobile, 2 columns on tablet, 4 on desktop */
    grid-template-columns: 1fr;
    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Additional mobile optimizations */
@layer utilities {
  /* Improve scrolling on mobile */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Better tap highlighting */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Improve focus visibility */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
  }

  /* Safe area padding for notched devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Prevent zoom on double tap */
  .no-zoom {
    touch-action: manipulation;
  }

  /* Mobile device optimizations */
  .mobile-device {
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Use dynamic viewport height */
    min-height: calc(var(--vh, 1vh) * 100);
  }

  .touch-device {
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
  }

  /* Improve button touch targets on mobile */
  @media (hover: none) and (pointer: coarse) {
    button, [role="button"], input[type="button"], input[type="submit"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better visual feedback for touches */
    button:active,
    .virtual-key:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }

    /* Remove hover effects on touch devices */
    button:hover,
    .virtual-key:hover {
      background-color: initial;
    }
  }

  /* Improve focus states for keyboard navigation */
  @media (hover: hover) and (pointer: fine) {
    button:focus-visible,
    input:focus-visible,
    select:focus-visible,
    textarea:focus-visible {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }
  }
}
