# Vercel 部署指南

## 问题诊断

您的项目是一个**全栈应用**（Express.js + React），但Vercel默认将其识别为静态网站，导致访问首页时显示代码而不是正常的网页。

## 解决方案

我已经为您的项目配置了正确的Vercel部署设置：

### 1. 创建的文件

- `vercel.json` - Vercel配置文件
- `api/index.ts` - Vercel Functions入口文件
- `VERCEL_DEPLOYMENT_GUIDE.md` - 本部署指南

### 2. 修改的文件

- `package.json` - 添加了`vercel-build`脚本

### 3. 部署步骤

1. **提交代码到Git仓库**
   ```bash
   git add .
   git commit -m "Add Vercel deployment configuration"
   git push
   ```

2. **在Vercel中重新部署**
   - 登录 [Vercel Dashboard](https://vercel.com/dashboard)
   - 找到您的项目
   - 点击 "Redeploy" 或触发新的部署

3. **环境变量设置**
   确保在Vercel项目设置中配置了必要的环境变量：
   - `DATABASE_URL` - 数据库连接字符串
   - `NODE_ENV=production`

### 4. 配置说明

**vercel.json 配置：**
- 使用 `@vercel/node` 运行时处理服务器端逻辑
- 所有路由都通过 `api/index.ts` 处理
- 静态文件由服务器端代码提供

**构建流程：**
1. `npm run vercel-build` 构建React应用到 `dist/public`
2. Vercel部署 `api/index.ts` 作为serverless函数
3. 服务器代码提供静态文件和API路由

### 5. 验证部署

部署完成后，您的网站应该：
- ✅ 正常显示React应用界面
- ✅ API路由正常工作
- ✅ 静态资源正确加载
- ✅ Google Analytics正常运行

### 6. 故障排除

如果仍有问题：

1. **检查Vercel构建日志**
   - 在Vercel Dashboard中查看部署日志
   - 确认构建过程没有错误

2. **检查函数日志**
   - 在Vercel Dashboard的Functions标签页查看运行时日志

3. **本地测试**
   ```bash
   npm run vercel-build
   npm run start
   ```

### 7. 项目结构

```
typingtest-web/
├── api/
│   └── index.ts          # Vercel Functions入口
├── client/
│   ├── src/              # React源代码
│   └── index.html        # HTML模板
├── server/
│   ├── index.ts          # 本地开发服务器
│   ├── routes.ts         # API路由
│   └── vite.ts           # Vite配置
├── dist/
│   └── public/           # 构建输出
├── vercel.json           # Vercel配置
└── package.json
```

现在您的项目应该能在Vercel上正确部署和运行了！
