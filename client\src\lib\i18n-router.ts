import { SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, isValidLanguage, type SupportedLanguage } from './i18n';

/**
 * Extract language from URL path
 * @param path - Current URL path
 * @returns Object with language and path without language prefix
 */
export function extractLanguageFromPath(path: string): {
  language: SupportedLanguage;
  pathWithoutLang: string;
} {
  const segments = path.split('/').filter(Boolean);
  
  if (segments.length > 0 && isValidLanguage(segments[0])) {
    return {
      language: segments[0],
      pathWithoutLang: '/' + segments.slice(1).join('/')
    };
  }
  
  return {
    language: DEFAULT_LANGUAGE,
    pathWithoutLang: path
  };
}

/**
 * Build URL with language prefix
 * @param language - Target language
 * @param path - Path without language prefix
 * @returns Complete URL with language prefix
 */
export function buildLanguageUrl(language: SupportedLanguage, path: string): string {
  // Remove leading slash from path if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // For default language, we can choose to include or exclude the prefix
  // Here we include it for consistency, but this can be configured
  return `/${language}${cleanPath ? '/' + cleanPath : ''}`;
}

/**
 * Get language from various sources (URL, query params, localStorage, browser)
 * @param currentPath - Current URL path
 * @param searchParams - URL search parameters
 * @returns Detected language
 */
export function detectLanguage(currentPath: string, searchParams?: URLSearchParams): SupportedLanguage {
  // 1. Try to get from URL path first
  const { language: pathLang } = extractLanguageFromPath(currentPath);
  if (pathLang !== DEFAULT_LANGUAGE) {
    return pathLang;
  }
  
  // 2. Try to get from query parameter (for backward compatibility)
  if (searchParams) {
    const queryLang = searchParams.get('lang');
    if (queryLang && isValidLanguage(queryLang)) {
      return queryLang;
    }
  }
  
  // 3. Try to get from localStorage
  try {
    const storedLang = localStorage.getItem('preferred-language');
    if (storedLang && isValidLanguage(storedLang)) {
      return storedLang;
    }
  } catch (e) {
    // localStorage might not be available
  }
  
  // 4. Try to get from browser language
  if (typeof navigator !== 'undefined') {
    const browserLang = navigator.language.split('-')[0];
    if (isValidLanguage(browserLang)) {
      return browserLang;
    }
  }
  
  // 5. Fall back to default
  return DEFAULT_LANGUAGE;
}

/**
 * Store language preference
 * @param language - Language to store
 */
export function storeLanguagePreference(language: SupportedLanguage): void {
  try {
    localStorage.setItem('preferred-language', language);
  } catch (e) {
    // localStorage might not be available
  }
}

/**
 * Check if current URL needs redirect for language consistency
 * @param currentPath - Current URL path
 * @param searchParams - URL search parameters
 * @returns Redirect URL if needed, null otherwise
 */
export function getLanguageRedirectUrl(currentPath: string, searchParams?: URLSearchParams): string | null {
  const { language: pathLang, pathWithoutLang } = extractLanguageFromPath(currentPath);
  
  // Check if we have a query parameter that should be converted to path
  if (searchParams) {
    const queryLang = searchParams.get('lang');
    if (queryLang && isValidLanguage(queryLang)) {
      // Redirect from query param to path-based URL
      return buildLanguageUrl(queryLang, pathWithoutLang);
    }
  }
  
  // If no language in path and no query param, detect and redirect
  if (pathLang === DEFAULT_LANGUAGE && !currentPath.startsWith(`/${DEFAULT_LANGUAGE}`)) {
    const detectedLang = detectLanguage(currentPath, searchParams);
    if (detectedLang !== DEFAULT_LANGUAGE) {
      return buildLanguageUrl(detectedLang, pathWithoutLang);
    }
    // For default language, we can choose to redirect to include the prefix or not
    // Here we redirect to include the prefix for consistency
    return buildLanguageUrl(DEFAULT_LANGUAGE, pathWithoutLang);
  }
  
  return null;
}

/**
 * Generate navigation URLs for language switching
 * @param currentPath - Current path without language prefix
 * @returns Object with URLs for all supported languages
 */
export function generateLanguageUrls(currentPath: string): Record<SupportedLanguage, string> {
  const urls = {} as Record<SupportedLanguage, string>;
  
  SUPPORTED_LANGUAGES.forEach(lang => {
    urls[lang] = buildLanguageUrl(lang, currentPath);
  });
  
  return urls;
}
