import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation, useRouter } from 'wouter';
import { 
  type SupportedLanguage, 
  DEFAULT_LANGUAGE,
  isValidLanguage 
} from '@/lib/i18n';
import { 
  extractLanguageFromPath, 
  detectLanguage, 
  storeLanguagePreference,
  getLanguageRedirectUrl,
  buildLanguageUrl
} from '@/lib/i18n-router';

interface LanguageContextType {
  language: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  pathWithoutLang: string;
  buildUrl: (path: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

interface LanguageProviderProps {
  children: React.ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps): JSX.Element {
  const [location, navigate] = useLocation();
  const [language, setLanguageState] = useState<SupportedLanguage>(DEFAULT_LANGUAGE);
  const [pathWithoutLang, setPathWithoutLang] = useState('/');

  // Update language and path when location changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.split('?')[1] || '');
    const currentPath = location.split('?')[0];
    
    // Check if we need to redirect for language consistency
    const redirectUrl = getLanguageRedirectUrl(currentPath, searchParams);
    if (redirectUrl) {
      navigate(redirectUrl, { replace: true });
      return;
    }
    
    // Extract language and path from current URL
    const { language: detectedLang, pathWithoutLang: detectedPath } = extractLanguageFromPath(currentPath);
    
    setLanguageState(detectedLang);
    setPathWithoutLang(detectedPath);
    
    // Store language preference
    storeLanguagePreference(detectedLang);
  }, [location, navigate]);

  const setLanguage = (newLanguage: SupportedLanguage) => {
    if (!isValidLanguage(newLanguage)) {
      console.warn(`Invalid language: ${newLanguage}`);
      return;
    }
    
    // Build new URL with the new language
    const newUrl = buildLanguageUrl(newLanguage, pathWithoutLang);
    navigate(newUrl);
  };

  const buildUrl = (path: string): string => {
    return buildLanguageUrl(language, path);
  };

  const contextValue: LanguageContextType = {
    language,
    setLanguage,
    pathWithoutLang,
    buildUrl
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}
