/**
 * Google Analytics 验证工具
 * 在浏览器控制台中运行此脚本来验证 Google Analytics 集成
 */

(function() {
  'use strict';

  // 配置
  const GA_MEASUREMENT_ID = 'G-RCM4MX2590';
  const GTAG_SCRIPT_URL = 'https://www.googletagmanager.com/gtag/js';
  
  // 验证结果存储
  const validationResults = {
    scriptTag: false,
    scriptLoaded: false,
    gtagFunction: false,
    dataLayer: false,
    configCalled: false,
    networkRequests: false,
    errors: [],
    warnings: []
  };

  /**
   * 主验证函数
   */
  function validateGoogleAnalytics() {
    console.log('🔍 开始验证 Google Analytics 集成...\n');
    
    // 1. 检查脚本标签
    validateScriptTag();
    
    // 2. 检查 gtag 函数
    validateGtagFunction();
    
    // 3. 检查 dataLayer
    validateDataLayer();
    
    // 4. 检查配置调用
    validateConfiguration();
    
    // 5. 检查网络请求
    validateNetworkRequests();
    
    // 6. 检查可能的阻止因素
    checkBlockingFactors();
    
    // 7. 显示结果
    displayResults();
    
    return validationResults;
  }

  /**
   * 检查 gtag 脚本标签
   */
  function validateScriptTag() {
    console.log('1️⃣ 检查 gtag 脚本标签...');
    
    const gtagScript = document.querySelector(`script[src*="${GTAG_SCRIPT_URL}"]`);
    
    if (gtagScript) {
      validationResults.scriptTag = true;
      console.log('✅ 找到 gtag 脚本标签');
      console.log(`   URL: ${gtagScript.src}`);
      console.log(`   Async: ${gtagScript.async}`);
      
      // 检查脚本是否已加载
      if (gtagScript.readyState === 'complete' || gtagScript.readyState === 'loaded') {
        validationResults.scriptLoaded = true;
        console.log('✅ 脚本已加载完成');
      } else {
        console.log('⏳ 脚本正在加载中...');
      }
    } else {
      validationResults.errors.push('未找到 gtag 脚本标签');
      console.log('❌ 未找到 gtag 脚本标签');
    }
  }

  /**
   * 检查 gtag 函数
   */
  function validateGtagFunction() {
    console.log('\n2️⃣ 检查 gtag 函数...');
    
    if (typeof window.gtag === 'function') {
      validationResults.gtagFunction = true;
      console.log('✅ gtag 函数已定义');
      
      // 测试 gtag 函数
      try {
        window.gtag('event', 'ga_validation_test', {
          'custom_parameter': 'validation_test',
          'send_to': GA_MEASUREMENT_ID
        });
        console.log('✅ gtag 函数测试调用成功');
      } catch (error) {
        validationResults.warnings.push(`gtag 函数调用出错: ${error.message}`);
        console.log(`⚠️ gtag 函数调用出错: ${error.message}`);
      }
    } else {
      validationResults.errors.push('gtag 函数未定义');
      console.log('❌ gtag 函数未定义');
    }
  }

  /**
   * 检查 dataLayer
   */
  function validateDataLayer() {
    console.log('\n3️⃣ 检查 dataLayer...');
    
    if (window.dataLayer && Array.isArray(window.dataLayer)) {
      validationResults.dataLayer = true;
      console.log(`✅ dataLayer 已定义，包含 ${window.dataLayer.length} 个项目`);
      
      if (window.dataLayer.length > 0) {
        console.log('   dataLayer 内容预览:');
        window.dataLayer.slice(0, 3).forEach((item, index) => {
          console.log(`   [${index}]:`, item);
        });
        
        if (window.dataLayer.length > 3) {
          console.log(`   ... 还有 ${window.dataLayer.length - 3} 个项目`);
        }
      }
    } else {
      validationResults.errors.push('dataLayer 未定义或不是数组');
      console.log('❌ dataLayer 未定义或不是数组');
    }
  }

  /**
   * 检查配置调用
   */
  function validateConfiguration() {
    console.log('\n4️⃣ 检查 GA 配置...');
    
    if (window.dataLayer && Array.isArray(window.dataLayer)) {
      const configCall = window.dataLayer.find(item => 
        Array.isArray(item) && item[0] === 'config' && item[1] === GA_MEASUREMENT_ID
      );
      
      if (configCall) {
        validationResults.configCalled = true;
        console.log('✅ GA 配置已调用');
        console.log('   配置参数:', configCall);
      } else {
        validationResults.errors.push('GA 配置未调用');
        console.log('❌ GA 配置未调用');
      }
    }
  }

  /**
   * 检查网络请求
   */
  function validateNetworkRequests() {
    console.log('\n5️⃣ 检查网络请求...');
    console.log('请在 Network 标签中查找以下请求:');
    console.log(`   📡 ${GTAG_SCRIPT_URL}?id=${GA_MEASUREMENT_ID}`);
    console.log('   📡 https://www.google-analytics.com/g/collect');
    console.log('   📡 https://www.google-analytics.com/j/collect');
    
    // 注意：由于安全限制，我们无法直接检查网络请求
    validationResults.warnings.push('请手动检查 Network 标签中的网络请求');
  }

  /**
   * 检查可能的阻止因素
   */
  function checkBlockingFactors() {
    console.log('\n6️⃣ 检查可能的阻止因素...');
    
    // 检查广告拦截器
    const testElement = document.createElement('div');
    testElement.innerHTML = '&nbsp;';
    testElement.className = 'adsbox';
    testElement.style.position = 'absolute';
    testElement.style.left = '-9999px';
    document.body.appendChild(testElement);
    
    setTimeout(() => {
      if (testElement.offsetHeight === 0) {
        validationResults.warnings.push('可能存在广告拦截器');
        console.log('⚠️ 可能存在广告拦截器');
      } else {
        console.log('✅ 未检测到广告拦截器');
      }
      document.body.removeChild(testElement);
    }, 100);
    
    // 检查 CSP
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta) {
      console.log('⚠️ 发现 CSP 策略:', cspMeta.content);
      if (!cspMeta.content.includes('googletagmanager.com')) {
        validationResults.warnings.push('CSP 策略可能阻止 Google Analytics');
      }
    } else {
      console.log('✅ 未发现限制性 CSP 策略');
    }
    
    // 检查环境
    const isLocalhost = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname === '';
    
    if (isLocalhost) {
      console.log('ℹ️ 当前在本地开发环境');
      validationResults.warnings.push('在本地开发环境中运行');
    }
  }

  /**
   * 显示验证结果
   */
  function displayResults() {
    console.log('\n📊 验证结果总结:');
    console.log('==================');
    
    const checks = [
      { name: '脚本标签', status: validationResults.scriptTag },
      { name: 'gtag 函数', status: validationResults.gtagFunction },
      { name: 'dataLayer', status: validationResults.dataLayer },
      { name: 'GA 配置', status: validationResults.configCalled }
    ];
    
    checks.forEach(check => {
      console.log(`${check.status ? '✅' : '❌'} ${check.name}`);
    });
    
    if (validationResults.errors.length > 0) {
      console.log('\n❌ 发现的错误:');
      validationResults.errors.forEach(error => console.log(`   • ${error}`));
    }
    
    if (validationResults.warnings.length > 0) {
      console.log('\n⚠️ 警告信息:');
      validationResults.warnings.forEach(warning => console.log(`   • ${warning}`));
    }
    
    const passedChecks = checks.filter(check => check.status).length;
    const totalChecks = checks.length;
    
    console.log(`\n🎯 总体状态: ${passedChecks}/${totalChecks} 项检查通过`);
    
    if (passedChecks === totalChecks && validationResults.errors.length === 0) {
      console.log('🎉 Google Analytics 集成验证通过！');
    } else {
      console.log('🔧 需要修复上述问题以确保 Google Analytics 正常工作');
    }
  }

  /**
   * 发送测试事件
   */
  function sendTestEvent() {
    console.log('\n🧪 发送测试事件...');
    
    if (typeof window.gtag === 'function') {
      const eventData = {
        'event_category': 'validation',
        'event_label': 'manual_test',
        'custom_parameter': `test_${Date.now()}`,
        'send_to': GA_MEASUREMENT_ID
      };
      
      window.gtag('event', 'validation_test', eventData);
      console.log('✅ 测试事件已发送');
      console.log('   事件数据:', eventData);
      console.log('   请检查 Network 标签中是否有新的 collect 请求');
    } else {
      console.log('❌ 无法发送测试事件：gtag 函数不可用');
    }
  }

  // 导出到全局作用域
  window.validateGoogleAnalytics = validateGoogleAnalytics;
  window.sendGATestEvent = sendTestEvent;
  
  console.log('🚀 Google Analytics 验证工具已加载！');
  console.log('使用方法:');
  console.log('  validateGoogleAnalytics() - 运行完整验证');
  console.log('  sendGATestEvent() - 发送测试事件');

})();
